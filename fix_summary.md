# حل مشكلة خطأ HTTP 400 في تحميل المنتجات

## المشكلة
كانت المنتجات المضافة للجلسات لا تظهر في صفحة الجلسات، وكان يظهر خطأ "HTTP error! status: 400".

## السبب الجذري
المشكلة كانت في **مسارات API غير صحيحة** في ملف `client/sessions.php`. كان الكود يستدعي APIs باستخدام مسارات نسبية خاطئة:

```javascript
// خطأ - مسار غير صحيح
fetch(`api/get_session_products.php?session_id=${sessionId}`)

// صحيح - المسار المصحح
fetch(`client/api/get_session_products.php?session_id=${sessionId}`)
```

## الملفات المصححة

### 1. client/sessions.php
تم إصلاح جميع استدعاءات API في هذا الملف:

#### استدعاءات get_session_products.php
- السطر 1286: `api/get_session_products.php` → `client/api/get_session_products.php`
- السطر 1496: `api/get_session_products.php` → `client/api/get_session_products.php`

#### استدعاءات get_products.php
- السطر 1165: `api/get_products.php` → `client/api/get_products.php`
- السطر 1731: `api/get_products.php` → `client/api/get_products.php`
- السطر 1912: `api/get_products.php` → `client/api/get_products.php`

#### استدعاءات get_session_details.php
- السطر 1424: `api/get_session_details.php` → `client/api/get_session_details.php`
- السطر 1867: `api/get_session_details.php` → `client/api/get_session_details.php`

#### استدعاءات add_session_product.php
- السطر 1235: `api/add_session_product.php` → `client/api/add_session_product.php`
- السطر 1810: `api/add_session_product.php` → `client/api/add_session_product.php`
- السطر 2093: `api/add_session_product.php` → `client/api/add_session_product.php`

#### استدعاءات delete_session_product.php
- السطر 1342: `api/delete_session_product.php` → `client/api/delete_session_product.php`
- السطر 1626: `api/delete_session_product.php` → `client/api/delete_session_product.php`

#### استدعاءات update_session.php
- السطر 2223: `api/update_session.php` → `client/api/update_session.php`

## الوظائف المتأثرة

### 1. تحميل المنتجات في modal التعديل
```javascript
function loadEditSessionProducts(sessionId) {
    // تم إصلاح المسار هنا
    fetch(`client/api/get_session_products.php?session_id=${sessionId}`)
}
```

### 2. تحديث المنتجات في الجدول الرئيسي
```javascript
function updateSessionProducts(sessionId) {
    // تم إصلاح المسار هنا
    fetch(`client/api/get_session_products.php?session_id=${sessionId}`)
}
```

### 3. إضافة المنتجات للجلسات
```javascript
function addProductToSession(sessionId, productId, quantity) {
    // تم إصلاح المسار هنا
    fetch('client/api/add_session_product.php', {
        method: 'POST',
        // ...
    })
}
```

### 4. حذف المنتجات من الجلسات
```javascript
function deleteSessionProduct(sessionId, productId) {
    // تم إصلاح المسار هنا
    fetch('client/api/delete_session_product.php', {
        method: 'POST',
        // ...
    })
}
```

## ملفات الاختبار المنشأة

### 1. debug_session_products_error.php
ملف شامل لتشخيص مشاكل API المنتجات

### 2. test_api_path.php
ملف لاختبار مسارات API والتحقق من وجود الملفات

### 3. check_sessions.php
ملف لفحص الجلسات الموجودة في قاعدة البيانات

### 4. test_fixed_api.php
ملف لاختبار API بعد إصلاح المسارات

## النتيجة
بعد إصلاح المسارات، يجب أن تعمل الوظائف التالية بشكل صحيح:

✅ عرض المنتجات المضافة للجلسات في modal التعديل
✅ إضافة منتجات جديدة للجلسات
✅ حذف المنتجات من الجلسات
✅ تحديث التكلفة الإجمالية للجلسات
✅ عرض تفاصيل الجلسات

## ملاحظات مهمة

1. **بنية المجلدات**: تأكد من أن ملفات API موجودة في `client/api/` وليس `api/`

2. **المسارات النسبية**: عند استدعاء APIs من ملفات في مجلد `client/`، يجب استخدام المسار النسبي الصحيح

3. **اختبار الوظائف**: يُنصح بتشغيل ملفات الاختبار للتأكد من عمل جميع الوظائف

4. **مراقبة الأخطاء**: استخدم Developer Tools في المتصفح لمراقبة أي أخطاء JavaScript أو HTTP

## التحقق من الحل

لاختبار أن المشكلة تم حلها:

1. افتح صفحة الجلسات: `http://localhost/playgood/client/sessions.php`
2. اضغط على "تعديل" لأي جلسة
3. يجب أن تظهر المنتجات المضافة (إن وجدت) بدون خطأ HTTP 400
4. جرب إضافة منتج جديد للجلسة
5. تأكد من تحديث التكلفة الإجمالية

إذا استمرت المشكلة، تحقق من:
- وجود ملفات API في المسار الصحيح
- صحة إعدادات قاعدة البيانات
- وجود جلسات وبيانات في قاعدة البيانات
