<?php
/**
 * اختبار API بعد إصلاح المسارات
 */

echo "<h1>اختبار API بعد إصلاح المسارات</h1>";
echo "<hr>";

session_start();
$_SESSION['client_id'] = 1; // محاكاة تسجيل الدخول

require_once 'config/database.php';

// البحث عن جلسة للاختبار
$stmt = $pdo->query("SELECT session_id FROM sessions ORDER BY session_id DESC LIMIT 1");
$session = $stmt->fetch();

if ($session) {
    $test_session_id = $session['session_id'];
    echo "<p style='color: green;'>✅ سيتم اختبار الجلسة رقم: $test_session_id</p>";
} else {
    // إنشاء جلسة تجريبية
    $device_stmt = $pdo->query("SELECT device_id FROM devices LIMIT 1");
    $device = $device_stmt->fetch();
    
    if (!$device) {
        $pdo->exec("INSERT INTO devices (device_name, device_type, hourly_rate, status, client_id) VALUES ('جهاز تجريبي', 'PS5', 15.00, 'available', 1)");
        $device_id = $pdo->lastInsertId();
    } else {
        $device_id = $device['device_id'];
    }
    
    $customer_stmt = $pdo->query("SELECT customer_id FROM customers LIMIT 1");
    $customer = $customer_stmt->fetch();
    
    if (!$customer) {
        $pdo->exec("INSERT INTO customers (name, phone, client_id) VALUES ('عميل تجريبي', '01234567890', 1)");
        $customer_id = $pdo->lastInsertId();
    } else {
        $customer_id = $customer['customer_id'];
    }
    
    $stmt = $pdo->prepare("INSERT INTO sessions (device_id, customer_id, status, start_time, client_id) VALUES (?, ?, 'active', NOW(), 1)");
    $stmt->execute([$device_id, $customer_id]);
    $test_session_id = $pdo->lastInsertId();
    echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $test_session_id</p>";
}

echo "<h2>اختبار JavaScript مع المسار المصحح</h2>";
?>

<div id="test-area" style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;">
    <h3>اختبار تحميل المنتجات:</h3>
    <button onclick="testAPI()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        اختبار API
    </button>
    <div id="results" style="margin-top: 15px;"></div>
</div>

<script>
const sessionId = <?php echo $test_session_id; ?>;

function testAPI() {
    const resultsDiv = document.getElementById('results');
    resultsDiv.innerHTML = '<p style="color: blue;">جاري الاختبار...</p>';
    
    console.log('Testing session ID:', sessionId);
    
    // اختبار المسار المصحح
    fetch(`client/api/get_session_products.php?session_id=${sessionId}`)
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response OK:', response.ok);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return response.text();
        })
        .then(responseText => {
            console.log('Raw response:', responseText);
            
            try {
                const data = JSON.parse(responseText);
                console.log('Parsed data:', data);
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div style="color: green;">
                            <h4>✅ نجح الاختبار!</h4>
                            <p><strong>معرف الجلسة:</strong> ${data.session_id}</p>
                            <p><strong>عدد المنتجات:</strong> ${data.products_count}</p>
                            <p><strong>التكلفة الإجمالية:</strong> ${data.total_cost} ج.م</p>
                        </div>
                    `;
                    
                    if (data.products && data.products.length > 0) {
                        let productsHtml = '<h5>المنتجات:</h5><ul>';
                        data.products.forEach(product => {
                            productsHtml += `<li>${product.product_name} - ${product.quantity} × ${product.price} = ${product.total} ج.م</li>`;
                        });
                        productsHtml += '</ul>';
                        resultsDiv.innerHTML += productsHtml;
                    } else {
                        resultsDiv.innerHTML += '<p style="color: orange;">⚠️ لا توجد منتجات مضافة لهذه الجلسة</p>';
                    }
                } else {
                    resultsDiv.innerHTML = `
                        <div style="color: red;">
                            <h4>❌ فشل الاختبار</h4>
                            <p><strong>الخطأ:</strong> ${data.error}</p>
                        </div>
                    `;
                    
                    if (data.debug_info) {
                        resultsDiv.innerHTML += `<details><summary>معلومات التشخيص</summary><pre>${JSON.stringify(data.debug_info, null, 2)}</pre></details>`;
                    }
                }
            } catch (jsonError) {
                console.error('JSON parsing error:', jsonError);
                resultsDiv.innerHTML = `
                    <div style="color: red;">
                        <h4>❌ خطأ في تحليل JSON</h4>
                        <p><strong>الخطأ:</strong> ${jsonError.message}</p>
                        <details>
                            <summary>الاستجابة الخام</summary>
                            <pre style="background: #ffe6e6; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;">${responseText}</pre>
                        </details>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            resultsDiv.innerHTML = `
                <div style="color: red;">
                    <h4>❌ خطأ في الشبكة</h4>
                    <p><strong>الخطأ:</strong> ${error.message}</p>
                </div>
            `;
        });
}

// تشغيل الاختبار تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(testAPI, 1000);
});
</script>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

#test-area {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

button {
    cursor: pointer;
}

button:hover {
    background: #0056b3 !important;
}

pre {
    font-size: 12px;
}
</style>
