<?php
/**
 * فحص الجلسات الموجودة في قاعدة البيانات
 */

echo "<h1>فحص الجلسات الموجودة</h1>";
echo "<hr>";

session_start();
$_SESSION['client_id'] = 1; // محاكاة تسجيل الدخول

require_once 'config/database.php';

echo "<h2>1. فحص جدول sessions</h2>";

try {
    // فحص وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'sessions'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "<p style='color: green;'>✅ جدول sessions موجود</p>";
        
        // فحص بنية الجدول
        $stmt = $pdo->query("DESCRIBE sessions");
        $columns = $stmt->fetchAll();
        
        echo "<h3>بنية الجدول:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // عدد الجلسات
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM sessions");
        $count = $stmt->fetch()['count'];
        echo "<p><strong>إجمالي عدد الجلسات:</strong> $count</p>";
        
        if ($count > 0) {
            // عرض آخر 10 جلسات
            $stmt = $pdo->query("SELECT * FROM sessions ORDER BY session_id DESC LIMIT 10");
            $sessions = $stmt->fetchAll();
            
            echo "<h3>آخر 10 جلسات:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>معرف الجلسة</th><th>معرف الجهاز</th><th>معرف العميل</th><th>الحالة</th><th>وقت البدء</th><th>وقت الانتهاء</th><th>معرف العميل (النظام)</th>";
            echo "</tr>";
            
            foreach ($sessions as $session) {
                echo "<tr>";
                echo "<td>{$session['session_id']}</td>";
                echo "<td>{$session['device_id']}</td>";
                echo "<td>{$session['customer_id']}</td>";
                echo "<td>{$session['status']}</td>";
                echo "<td>{$session['start_time']}</td>";
                echo "<td>" . ($session['end_time'] ?? 'لم تنته') . "</td>";
                echo "<td>" . ($session['client_id'] ?? 'غير محدد') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // اختبار API مع جلسة حقيقية
            $test_session = $sessions[0];
            $test_session_id = $test_session['session_id'];
            
            echo "<h2>2. اختبار API مع الجلسة رقم: $test_session_id</h2>";
            
            $api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/get_session_products.php?session_id=' . $test_session_id;
            echo "<p>🔗 رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";
            
            // اختبار API
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => 'Cookie: ' . session_name() . '=' . session_id(),
                    'timeout' => 10
                ]
            ]);
            
            $response = @file_get_contents($api_url, false, $context);
            
            if ($response !== false) {
                echo "<p style='color: green;'>✅ API استجاب بنجاح</p>";
                echo "<h3>الاستجابة:</h3>";
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
                echo htmlspecialchars($response);
                echo "</pre>";
                
                // تحليل JSON
                $data = json_decode($response, true);
                if ($data) {
                    echo "<h3>البيانات المحللة:</h3>";
                    echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                    
                    if (isset($data['success']) && $data['success']) {
                        echo "<p style='color: green;'>✅ API يعمل بشكل صحيح</p>";
                    } else {
                        echo "<p style='color: red;'>❌ API أرجع خطأ: " . ($data['error'] ?? 'خطأ غير معروف') . "</p>";
                    }
                }
            } else {
                echo "<p style='color: red;'>❌ فشل في استدعاء API</p>";
                $error = error_get_last();
                if ($error) {
                    echo "<p style='color: red;'>الخطأ: " . $error['message'] . "</p>";
                }
            }
            
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد جلسات في قاعدة البيانات</p>";
            
            // إنشاء جلسة تجريبية
            echo "<h2>2. إنشاء جلسة تجريبية</h2>";
            
            // التحقق من وجود جهاز
            $device_stmt = $pdo->query("SELECT device_id FROM devices LIMIT 1");
            $device = $device_stmt->fetch();
            
            if (!$device) {
                $pdo->exec("INSERT INTO devices (device_name, device_type, hourly_rate, status, client_id) VALUES ('جهاز تجريبي', 'PS5', 15.00, 'available', 1)");
                $device_id = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ تم إنشاء جهاز تجريبي: $device_id</p>";
            } else {
                $device_id = $device['device_id'];
                echo "<p style='color: blue;'>ℹ️ استخدام الجهاز الموجود: $device_id</p>";
            }
            
            // التحقق من وجود عميل
            $customer_stmt = $pdo->query("SELECT customer_id FROM customers LIMIT 1");
            $customer = $customer_stmt->fetch();
            
            if (!$customer) {
                $pdo->exec("INSERT INTO customers (name, phone, client_id) VALUES ('عميل تجريبي', '01234567890', 1)");
                $customer_id = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ تم إنشاء عميل تجريبي: $customer_id</p>";
            } else {
                $customer_id = $customer['customer_id'];
                echo "<p style='color: blue;'>ℹ️ استخدام العميل الموجود: $customer_id</p>";
            }
            
            // إنشاء جلسة تجريبية
            $stmt = $pdo->prepare("INSERT INTO sessions (device_id, customer_id, status, start_time, client_id) VALUES (?, ?, 'active', NOW(), 1)");
            $stmt->execute([$device_id, $customer_id]);
            $test_session_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $test_session_id</p>";
            
            // اختبار API مع الجلسة الجديدة
            echo "<h2>3. اختبار API مع الجلسة الجديدة</h2>";
            
            $api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/playgood/client/api/get_session_products.php?session_id=' . $test_session_id;
            echo "<p>🔗 رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";
            
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => 'Cookie: ' . session_name() . '=' . session_id(),
                    'timeout' => 10
                ]
            ]);
            
            $response = @file_get_contents($api_url, false, $context);
            
            if ($response !== false) {
                echo "<p style='color: green;'>✅ API استجاب بنجاح</p>";
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
                echo htmlspecialchars($response);
                echo "</pre>";
            } else {
                echo "<p style='color: red;'>❌ فشل في استدعاء API</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ جدول sessions غير موجود</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>3. فحص جدول session_products</h2>";

try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'session_products'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "<p style='color: green;'>✅ جدول session_products موجود</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM session_products");
        $count = $stmt->fetch()['count'];
        echo "<p><strong>إجمالي عدد المنتجات:</strong> $count</p>";
        
        if ($count > 0) {
            $stmt = $pdo->query("SELECT sp.*, ci.name as product_name FROM session_products sp LEFT JOIN cafeteria_items ci ON sp.product_id = ci.id ORDER BY sp.id DESC LIMIT 5");
            $products = $stmt->fetchAll();
            
            echo "<h3>آخر 5 منتجات:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>ID</th><th>معرف الجلسة</th><th>معرف المنتج</th><th>اسم المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th>";
            echo "</tr>";
            
            foreach ($products as $product) {
                echo "<tr>";
                echo "<td>{$product['id']}</td>";
                echo "<td>{$product['session_id']}</td>";
                echo "<td>{$product['product_id']}</td>";
                echo "<td>" . ($product['product_name'] ?? 'غير معروف') . "</td>";
                echo "<td>{$product['quantity']}</td>";
                echo "<td>" . ($product['price'] ?? $product['unit_price'] ?? 0) . "</td>";
                echo "<td>" . ($product['total_price'] ?? $product['total'] ?? 0) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>❌ جدول session_products غير موجود</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص جدول session_products: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th {
    background: #007bff !important;
    color: white !important;
    padding: 8px;
}

td {
    padding: 6px;
    border: 1px solid #ddd;
}

pre {
    font-size: 12px;
}
</style>
