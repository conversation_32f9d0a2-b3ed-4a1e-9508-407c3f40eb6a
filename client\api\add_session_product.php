<?php
// تنظيف أي إخراج سابق
if (ob_get_level()) {
    ob_clean();
}

// تعيين headers قبل أي إخراج
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// محاولة تضمين ملفات التكوين مع معالجة الأخطاء
try {
    require_once '../../config/database.php';
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الاتصال بقاعدة البيانات',
        'debug' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// التحقق من تسجيل الدخول بدون إعادة توجيه
if (!isset($_SESSION['client_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'يجب تسجيل الدخول أولاً',
        'redirect' => '../login.php'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة طلب غير صحيحة');
    }

    $data = json_decode(file_get_contents('php://input'), true);

    // التحقق من صحة JSON
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('بيانات JSON غير صحيحة');
    }

    // التحقق من البيانات المطلوبة
    if (!isset($data['session_id']) || !isset($data['product_id']) || !isset($data['quantity'])) {
        throw new Exception('بيانات غير مكتملة - مطلوب: session_id, product_id, quantity');
    }

    // التحقق من صحة البيانات
    $session_id = intval($data['session_id']);
    $product_id = intval($data['product_id']);
    $quantity = intval($data['quantity']);

    if ($session_id <= 0 || $product_id <= 0 || $quantity <= 0) {
        throw new Exception('بيانات غير صحيحة - يجب أن تكون الأرقام موجبة');
    }

    $pdo->beginTransaction();

    // التحقق من وجود الجلسة وأنها نشطة
    $stmt = $pdo->prepare("SELECT session_id, status FROM sessions WHERE session_id = ? AND status = 'active'");
    $stmt->execute([$session_id]);
    $session_check = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$session_check) {
        throw new Exception('الجلسة غير موجودة أو غير نشطة');
    }

    // جلب معلومات المنتج
    $stmt = $pdo->prepare("SELECT id, name, price FROM cafeteria_items WHERE id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        throw new Exception('المنتج غير موجود');
    }

    // التحقق من وجود جدول session_products
    try {
        $pdo->query("SELECT 1 FROM session_products LIMIT 1");
    } catch (PDOException $e) {
        // إنشاء الجدول إذا لم يكن موجود
        $create_table = "
            CREATE TABLE session_products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_session_products_session_id (session_id),
                INDEX idx_session_products_product_id (product_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($create_table);
    }

    // إضافة المنتج للجلسة
    $unit_price = $product['price'];
    $total_price = $unit_price * $quantity;
    $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([$session_id, $product_id, $quantity, $unit_price, $total_price]);

    // حساب التكلفة الجديدة
    $stmt = $pdo->prepare("
        SELECT
            s.session_id,
            s.start_time,
            d.single_rate,
            d.hourly_rate,
            COALESCE(SUM(sp.total_price), 0) as products_cost,
            TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN session_products sp ON s.session_id = sp.session_id
        WHERE s.session_id = ?
        GROUP BY s.session_id, s.start_time, d.single_rate, d.hourly_rate
    ");
    $stmt->execute([$data['session_id']]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);

    // حساب التكلفة الكلية - استخدام single_rate أولاً
    $hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
    $time_cost = $session['duration_minutes'] > 0 && $hourly_rate > 0 ? ceil($session['duration_minutes'] / 60) * $hourly_rate : 0;
    $total_cost = $time_cost + $session['products_cost'];

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'تم إضافة المنتج بنجاح',
        'product_name' => $product['name'],
        'total_cost' => number_format($total_cost, 2),
        'products_cost' => number_format($session['products_cost'], 2),
        'time_cost' => number_format($time_cost, 2),
        'session_id' => $session_id,
        'product_id' => $product_id,
        'quantity' => $quantity
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // تسجيل الخطأ للمطورين
    error_log("Session Product Error: " . $e->getMessage() . " - Data: " . json_encode($data ?? []));

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug_info' => [
            'session_id' => $session_id ?? null,
            'product_id' => $product_id ?? null,
            'quantity' => $quantity ?? null,
            'client_id' => $_SESSION['client_id'] ?? null
        ]
    ], JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // تسجيل خطأ قاعدة البيانات
    error_log("Database Error in add_session_product: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ في قاعدة البيانات أثناء إضافة المنتج',
        'debug_error' => $e->getMessage(),
        'sql_state' => $e->getCode()
    ], JSON_UNESCAPED_UNICODE);
} catch (Throwable $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // تسجيل أي خطأ آخر
    error_log("Unexpected Error in add_session_product: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ غير متوقع',
        'debug_error' => $e->getMessage(),
        'error_type' => get_class($e)
    ], JSON_UNESCAPED_UNICODE);
}