<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة إدارة الموظفين
    if (!hasPagePermission('employees')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة إدارة الموظفين';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين - فقط المديرين يمكنهم إدارة الموظفين
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('employees')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_employees')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "إدارة الموظفين";
$active_page = "employees";

// إضافة موظف جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_employee'])) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO employees (
                name, phone, role, salary, hire_date, 
                username, password_hash, client_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $password = bin2hex(random_bytes(4)); // إنشاء كلمة مرور عشوائية
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        $username = strtolower(str_replace(' ', '', $_POST['name'])) . rand(100, 999);
        
        $stmt->execute([
            $_POST['name'],
            $_POST['phone'],
            $_POST['role'],
            $_POST['salary'],
            $_POST['hire_date'],
            $username,
            $password_hash,
            $client_id
        ]);
        
        $_SESSION['success'] = "تم إضافة الموظف بنجاح. اسم المستخدم: $username | كلمة المرور: $password";
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ أثناء إضافة الموظف";
    }
    header('Location: employees.php');
    exit;
}

// حذف موظف
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM employees WHERE id = ? AND client_id = ?");
        $stmt->execute([$_GET['delete'], $client_id]);
        $_SESSION['success'] = "تم حذف الموظف بنجاح";
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ أثناء حذف الموظف";
    }
    header('Location: employees.php');
    exit;
}

// تعديل بيانات الموظف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_employee'])) {
    try {
        $stmt = $pdo->prepare("
            UPDATE employees SET 
                name = ?, 
                phone = ?, 
                role = ?, 
                salary = ?, 
                hire_date = ?
            WHERE id = ? AND client_id = ?
        ");
        
        $stmt->execute([
            $_POST['name'],
            $_POST['phone'],
            $_POST['role'],
            $_POST['salary'],
            $_POST['hire_date'],
            $_POST['employee_id'],
            $client_id
        ]);
        
        $_SESSION['success'] = "تم تعديل بيانات الموظف بنجاح";
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ أثناء تعديل بيانات الموظف";
    }
    header('Location: employees.php');
    exit;
}

// إعادة تعيين كلمة المرور
if (isset($_GET['reset_password']) && is_numeric($_GET['reset_password'])) {
    try {
        $password = bin2hex(random_bytes(4));
        $password_hash = password_hash($password, PASSWORD_DEFAULT);

        $stmt = $pdo->prepare("
            UPDATE employees
            SET password_hash = ?
            WHERE id = ? AND client_id = ?
        ");

        $stmt->execute([$password_hash, $_GET['reset_password'], $client_id]);
        $_SESSION['success'] = "تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة: $password";
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ أثناء إعادة تعيين كلمة المرور";
    }
    header('Location: employees.php');
    exit;
}

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json');

    // تعديل بيانات الموظف
    if (isset($_POST['edit_employee'])) {
        try {
            $stmt = $pdo->prepare("
                UPDATE employees SET
                    name = ?,
                    phone = ?,
                    role = ?,
                    salary = ?,
                    hire_date = ?
                WHERE id = ? AND client_id = ?
            ");

            $stmt->execute([
                $_POST['name'],
                $_POST['phone'],
                $_POST['role'],
                $_POST['salary'],
                $_POST['hire_date'],
                $_POST['employee_id'],
                $client_id
            ]);

            // تحديد لون ونص الدور
            $roleColors = [
                'manager' => 'bg-primary',
                'cashier' => 'bg-success',
                'waiter' => 'bg-info',
                'cleaner' => 'bg-warning'
            ];
            $roleTexts = [
                'manager' => 'مدير',
                'cashier' => 'كاشير',
                'waiter' => 'ويتر',
                'cleaner' => 'عامل نظافة'
            ];

            echo json_encode([
                'success' => true,
                'message' => 'تم تعديل بيانات الموظف بنجاح',
                'data' => [
                    'name' => $_POST['name'],
                    'phone' => $_POST['phone'],
                    'role' => $_POST['role'],
                    'role_text' => $roleTexts[$_POST['role']] ?? $_POST['role'],
                    'role_color' => $roleColors[$_POST['role']] ?? 'bg-secondary',
                    'salary' => number_format($_POST['salary'], 2),
                    'hire_date' => $_POST['hire_date']
                ]
            ]);
            exit;

        } catch (PDOException $e) {
            echo json_encode([
                'success' => false,
                'message' => 'حدث خطأ أثناء تعديل بيانات الموظف'
            ]);
            exit;
        }
    }

    // تحديث الصلاحيات
    if (isset($_POST['update_permissions'])) {
        try {
            $employee_id = $_POST['employee_id'];
            $use_custom_permissions = isset($_POST['use_custom_permissions']) ? 1 : 0;

            // تحديث نوع الصلاحيات
            $stmt = $pdo->prepare("UPDATE employees SET custom_permissions = ? WHERE id = ? AND client_id = ?");
            $stmt->execute([$use_custom_permissions, $employee_id, $client_id]);

            if ($use_custom_permissions && !empty($permissions)) {
                $selected_permissions = $_POST['permissions'] ?? [];
                $selected_pages = $_POST['pages'] ?? [];

                $pdo->beginTransaction();

                // حذف الصلاحيات الحالية
                $stmt = $pdo->prepare("DELETE FROM employee_permissions WHERE employee_id = ?");
                $stmt->execute([$employee_id]);

                // إضافة الصلاحيات الجديدة
                if (!empty($selected_permissions)) {
                    $stmt = $pdo->prepare("INSERT INTO employee_permissions (employee_id, permission_id) VALUES (?, ?)");
                    foreach ($selected_permissions as $permission_id) {
                        $stmt->execute([$employee_id, $permission_id]);
                    }
                }

                // حذف الصفحات الحالية
                $stmt = $pdo->prepare("DELETE FROM employee_pages WHERE employee_id = ?");
                $stmt->execute([$employee_id]);

                // إضافة الصفحات الجديدة
                if (!empty($selected_pages)) {
                    $stmt = $pdo->prepare("INSERT INTO employee_pages (employee_id, page_id) VALUES (?, ?)");
                    foreach ($selected_pages as $page_id) {
                        $stmt->execute([$employee_id, $page_id]);
                    }
                }

                $pdo->commit();
            }

            $permission_type = $use_custom_permissions ? 'صلاحيات مخصصة' : 'صلاحيات الدور';
            $badge_class = $use_custom_permissions ? 'bg-info' : 'bg-secondary';

            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث صلاحيات الموظف بنجاح',
                'permission_type' => $permission_type,
                'badge_class' => $badge_class
            ]);
            exit;

        } catch (PDOException $e) {
            if (isset($pdo) && $pdo->inTransaction()) {
                $pdo->rollBack();
            }
            echo json_encode([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الصلاحيات'
            ]);
            exit;
        }
    }
}

// تحديث صلاحيات الموظف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_permissions'])) {
    try {
        $employee_id = $_POST['employee_id'];
        $use_custom_permissions = isset($_POST['use_custom_permissions']) ? 1 : 0;
        $selected_permissions = $_POST['permissions'] ?? [];
        $selected_pages = $_POST['pages'] ?? [];

        $pdo->beginTransaction();

        // تحديث نوع الصلاحيات
        $stmt = $pdo->prepare("UPDATE employees SET custom_permissions = ? WHERE id = ? AND client_id = ?");
        $stmt->execute([$use_custom_permissions, $employee_id, $client_id]);

        if ($use_custom_permissions) {
            // حذف الصلاحيات الحالية
            $stmt = $pdo->prepare("DELETE FROM employee_permissions WHERE employee_id = ?");
            $stmt->execute([$employee_id]);

            // إضافة الصلاحيات الجديدة
            if (!empty($selected_permissions)) {
                $stmt = $pdo->prepare("INSERT INTO employee_permissions (employee_id, permission_id) VALUES (?, ?)");
                foreach ($selected_permissions as $permission_id) {
                    $stmt->execute([$employee_id, $permission_id]);
                }
            }

            // حذف الصفحات الحالية
            $stmt = $pdo->prepare("DELETE FROM employee_pages WHERE employee_id = ?");
            $stmt->execute([$employee_id]);

            // إضافة الصفحات الجديدة
            if (!empty($selected_pages)) {
                $stmt = $pdo->prepare("INSERT INTO employee_pages (employee_id, page_id) VALUES (?, ?)");
                foreach ($selected_pages as $page_id) {
                    $stmt->execute([$employee_id, $page_id]);
                }
            }
        }

        $pdo->commit();
        $_SESSION['success'] = "تم تحديث صلاحيات الموظف بنجاح";
    } catch (PDOException $e) {
        $pdo->rollBack();
        $_SESSION['error'] = "حدث خطأ أثناء تحديث الصلاحيات";
    }
    header('Location: employees.php');
    exit;
}

// جلب قائمة الموظفين
$employees = $pdo->prepare("
    SELECT * FROM employees
    WHERE client_id = ?
    ORDER BY hire_date DESC");
$employees->execute([$client_id]);
$employees = $employees->fetchAll();

// جلب جميع الصلاحيات المتاحة (مع التحقق من وجود الجدول)
$permissions = [];
$pages = [];
try {
    $permissions = $pdo->query("
        SELECT * FROM permissions
        WHERE is_active = 1
        ORDER BY category, permission_label
    ")->fetchAll();

    // جلب جميع الصفحات المتاحة
    $pages = $pdo->query("
        SELECT * FROM pages
        WHERE is_active = 1
        ORDER BY category, page_label
    ")->fetchAll();
} catch (PDOException $e) {
    // الجداول غير موجودة - استخدام النظام التقليدي
    $permissions = [];
    $pages = [];
}

// تجميع الصلاحيات حسب الفئة
$permissions_by_category = [];
foreach ($permissions as $permission) {
    $permissions_by_category[$permission['category']][] = $permission;
}

// تجميع الصفحات حسب الفئة
$pages_by_category = [];
foreach ($pages as $page) {
    $pages_by_category[$page['category']][] = $page;
}

require_once 'includes/header.php';
?>

<!-- تضمين ملف CSS المخصص للصلاحيات -->
<link rel="stylesheet" href="assets/css/permissions.css">

<style>
    /* منع الوميض والانتقالات السلسة */
    .table tbody tr {
        transition: background-color 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    /* تحسين النوافذ المنبثقة */
    .modal-content {
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .modal-header {
        border-bottom: 1px solid #e9ecef;
        padding: 1rem 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    /* تحسين الأزرار */
    .btn {
        transition: all 0.2s ease;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* تحسين الرسائل */
    .alert {
        border: none;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* تحسين البطاقات */
    .card {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        transition: box-shadow 0.2s ease;
    }

    .card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* تحسين الـ checkboxes */
    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .form-check-label {
        cursor: pointer;
    }

    /* تحسين التبديل */
    .form-switch .form-check-input {
        width: 2em;
        height: 1em;
    }

    /* تحسين التحميل */
    .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* تحسين التمرير السلس */
    html {
        scroll-behavior: smooth;
    }

    /* تحسين الجدول المتجاوب */
    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* منع الوميض عند التحديث */
    .updating {
        opacity: 0.7;
        pointer-events: none;
    }

    /* تحسين الانتقالات */
    * {
        transition: opacity 0.2s ease;
    }
</style>

<div class="container-fluid py-4">
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- إضافة موظف جديد -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>إضافة موظف جديد</h5>
        </div>
        <div class="card-body">
            <form action="" method="POST" class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">اسم الموظف</label>
                    <input type="text" name="name" class="form-control" required>
                </div>
                <div class="col-md-6">
                    <label class="form-label">رقم الهاتف</label>
                    <input type="tel" name="phone" class="form-control" required>
                </div>
                <div class="col-md-4">
                    <label class="form-label">الوظيفة</label>
                    <select name="role" class="form-select" required>
                        <option value="manager">مدير</option>
                        <option value="cashier">كاشير</option>
                        <option value="waiter">ويتر</option>
                        <option value="cleaner">عامل نظافة</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">الراتب</label>
                    <input type="number" name="salary" class="form-control" required>
                </div>
                <div class="col-md-4">
                    <label class="form-label">تاريخ التعيين</label>
                    <input type="date" name="hire_date" class="form-control" required>
                </div>
                <div class="col-12">
                    <button type="submit" name="add_employee" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i>إضافة الموظف
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة الموظفين -->
    <div class="card shadow-sm">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0"><i class="fas fa-users me-2"></i>قائمة الموظفين</h5>
        </div>
        <div class="card-body">
            <?php if (count($employees) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الاسم</th>
                                <th>الوظيفة</th>
                                <th>نوع الصلاحيات</th>
                                <th>رقم الهاتف</th>
                                <th>الراتب</th>
                                <th>تاريخ التعيين</th>
                                <th>اسم المستخدم</th>
                                <th>آخر تسجيل دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($employees as $index => $employee): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td><?php echo htmlspecialchars($employee['name']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php
                                            echo match($employee['role']) {
                                                'manager' => 'primary',
                                                'cashier' => 'success',
                                                'waiter' => 'info',
                                                'cleaner' => 'warning',
                                                default => 'secondary'
                                            };
                                        ?>">
                                            <?php echo match($employee['role']) {
                                                'manager' => 'مدير',
                                                'cashier' => 'كاشير',
                                                'waiter' => 'ويتر',
                                                'cleaner' => 'عامل نظافة',
                                                default => $employee['role']
                                            }; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($permissions)): ?>
                                            <?php if ($employee['custom_permissions']): ?>
                                                <span class="badge bg-info">صلاحيات مخصصة</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">صلاحيات الدور</span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-primary">صلاحيات الدور</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($employee['phone']); ?></td>
                                    <td><?php echo number_format($employee['salary'], 2); ?> ج.م</td>
                                    <td><?php echo date('Y-m-d', strtotime($employee['hire_date'])); ?></td>
                                    <td><?php echo htmlspecialchars($employee['username']); ?></td>
                                    <td><?php echo $employee['last_login'] ? date('Y-m-d H:i', strtotime($employee['last_login'])) : 'لم يسجل دخول بعد'; ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" data-bs-toggle="modal" data-bs-target="#editModal<?php echo $employee['id']; ?>" title="تعديل البيانات">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php if (!empty($permissions)): ?>
                                        <a href="employee_permissions.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-success me-1" title="إدارة الصلاحيات">
                                            <i class="fas fa-shield-alt"></i>
                                        </a>
                                        <?php endif; ?>
                                        <button class="btn btn-sm btn-info me-1" onclick="resetPassword(<?php echo $employee['id']; ?>)" title="إعادة تعيين كلمة المرور">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteEmployee(<?php echo $employee['id']; ?>)" title="حذف الموظف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- Modal تعديل الموظف -->
                                <div class="modal fade" id="editModal<?php echo $employee['id']; ?>" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">تعديل بيانات الموظف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <form action="" method="POST" class="employee-edit-form">
                                                <div class="modal-body">
                                                    <input type="hidden" name="employee_id" value="<?php echo $employee['id']; ?>">
                                                    <input type="hidden" name="ajax" value="1">
                                                    <div class="mb-3">
                                                        <label class="form-label">اسم الموظف</label>
                                                        <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($employee['name']); ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">رقم الهاتف</label>
                                                        <input type="tel" name="phone" class="form-control" value="<?php echo htmlspecialchars($employee['phone']); ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">الوظيفة</label>
                                                        <select name="role" class="form-select" required>
                                                            <option value="manager" <?php echo $employee['role'] == 'manager' ? 'selected' : ''; ?>>مدير</option>
                                                            <option value="cashier" <?php echo $employee['role'] == 'cashier' ? 'selected' : ''; ?>>كاشير</option>
                                                            <option value="waiter" <?php echo $employee['role'] == 'waiter' ? 'selected' : ''; ?>>ويتر</option>
                                                            <option value="cleaner" <?php echo $employee['role'] == 'cleaner' ? 'selected' : ''; ?>>عامل نظافة</option>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">الراتب</label>
                                                        <input type="number" name="salary" class="form-control" value="<?php echo $employee['salary']; ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">تاريخ التعيين</label>
                                                        <input type="date" name="hire_date" class="form-control" value="<?php echo $employee['hire_date']; ?>" required>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                    <button type="submit" name="edit_employee" class="btn btn-primary">حفظ التغييرات</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>


                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد موظفين حالياً</h5>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function deleteEmployee(employeeId) {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
        window.location.href = `employees.php?delete=${employeeId}`;
    }
}

function resetPassword(employeeId) {
    if (confirm('هل أنت متأكد من إعادة تعيين كلمة المرور؟')) {
        window.location.href = `employees.php?reset_password=${employeeId}`;
    }
}



// إخفاء الرسائل تلقائياً بعد 5 ثوان
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert && alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            }
        }, 5000);
    });


});
</script>

<?php require_once 'includes/footer.php'; ?>