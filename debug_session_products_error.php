<?php
/**
 * تشخيص مشكلة خطأ HTTP 400 في تحميل المنتجات
 * PlayGood Gaming Center Management System
 */

echo "<h1>تشخيص مشكلة خطأ HTTP 400 في تحميل المنتجات</h1>";
echo "<hr>";

// بدء الجلسة
session_start();

// محاكاة تسجيل الدخول
if (!isset($_SESSION['client_id'])) {
    $_SESSION['client_id'] = 1;
    echo "<p style='color: blue;'>ℹ️ تم تعيين معرف العميل للاختبار: 1</p>";
}

require_once 'config/database.php';

echo "<h2>1. فحص الجلسات الموجودة</h2>";

try {
    // البحث عن جلسات موجودة
    $stmt = $pdo->query("SELECT session_id, device_id, customer_id, status, start_time FROM sessions ORDER BY session_id DESC LIMIT 5");
    $sessions = $stmt->fetchAll();
    
    if ($sessions) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>معرف الجلسة</th><th>معرف الجهاز</th><th>معرف العميل</th><th>الحالة</th><th>وقت البدء</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session['session_id']}</td>";
            echo "<td>{$session['device_id']}</td>";
            echo "<td>{$session['customer_id']}</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>{$session['start_time']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // استخدام أول جلسة للاختبار
        $test_session_id = $sessions[0]['session_id'];
        echo "<p style='color: green;'>✅ سيتم استخدام الجلسة رقم: $test_session_id للاختبار</p>";
        
    } else {
        echo "<p style='color: red;'>❌ لا توجد جلسات في قاعدة البيانات</p>";
        
        // إنشاء جلسة تجريبية
        echo "<h3>إنشاء جلسة تجريبية:</h3>";
        
        // التحقق من وجود جهاز
        $device_stmt = $pdo->query("SELECT device_id FROM devices LIMIT 1");
        $device = $device_stmt->fetch();
        
        if (!$device) {
            $pdo->exec("INSERT INTO devices (device_name, device_type, hourly_rate, status, client_id) VALUES ('جهاز تجريبي', 'PS5', 15.00, 'available', 1)");
            $device_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إنشاء جهاز تجريبي: $device_id</p>";
        } else {
            $device_id = $device['device_id'];
            echo "<p style='color: blue;'>ℹ️ استخدام الجهاز الموجود: $device_id</p>";
        }
        
        // التحقق من وجود عميل
        $customer_stmt = $pdo->query("SELECT customer_id FROM customers LIMIT 1");
        $customer = $customer_stmt->fetch();
        
        if (!$customer) {
            $pdo->exec("INSERT INTO customers (name, phone, client_id) VALUES ('عميل تجريبي', '01234567890', 1)");
            $customer_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ تم إنشاء عميل تجريبي: $customer_id</p>";
        } else {
            $customer_id = $customer['customer_id'];
            echo "<p style='color: blue;'>ℹ️ استخدام العميل الموجود: $customer_id</p>";
        }
        
        // إنشاء جلسة تجريبية
        $stmt = $pdo->prepare("INSERT INTO sessions (device_id, customer_id, status, start_time, client_id) VALUES (?, ?, 'active', NOW(), 1)");
        $stmt->execute([$device_id, $customer_id]);
        $test_session_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إنشاء جلسة تجريبية: $test_session_id</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>2. اختبار API مباشرة</h2>";

// اختبار API مباشرة
$api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/client/api/get_session_products.php?session_id=' . $test_session_id;
echo "<p>🔗 رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";

// اختبار API عبر cURL مع تفاصيل أكثر
$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        'Cookie: ' . session_name() . '=' . session_id()
    ],
    CURLOPT_TIMEOUT => 10,
    CURLOPT_VERBOSE => true,
    CURLOPT_STDERR => fopen('php://temp', 'w+')
]);

$response = curl_exec($curl);
$http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$curl_error = curl_error($curl);

// قراءة معلومات verbose
rewind(curl_getinfo($curl, CURLOPT_STDERR));
$verbose_log = stream_get_contents(curl_getinfo($curl, CURLOPT_STDERR));

curl_close($curl);

echo "<p style='color: blue;'>ℹ️ HTTP Code: $http_code</p>";

if ($curl_error) {
    echo "<p style='color: red;'>❌ خطأ cURL: $curl_error</p>";
}

echo "<h3>الاستجابة الخام:</h3>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
echo htmlspecialchars($response);
echo "</pre>";

if ($http_code == 200) {
    echo "<p style='color: green;'>✅ API يعمل بشكل صحيح</p>";
    
    // محاولة تحليل JSON
    $data = json_decode($response, true);
    if ($data) {
        echo "<h3>البيانات المحللة:</h3>";
        echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا يمكن تحليل الاستجابة كـ JSON</p>";
    }
} else {
    echo "<p style='color: red;'>❌ API أرجع خطأ HTTP: $http_code</p>";
    
    // محاولة تحليل رسالة الخطأ
    $error_data = json_decode($response, true);
    if ($error_data && isset($error_data['error'])) {
        echo "<p style='color: red;'><strong>رسالة الخطأ:</strong> " . htmlspecialchars($error_data['error']) . "</p>";
        
        if (isset($error_data['debug_info'])) {
            echo "<h3>معلومات التشخيص:</h3>";
            echo "<pre>" . json_encode($error_data['debug_info'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        }
    }
}

echo "<h2>3. فحص جدول session_products</h2>";

try {
    // فحص وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'session_products'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "<p style='color: green;'>✅ جدول session_products موجود</p>";
        
        // فحص بنية الجدول
        $stmt = $pdo->query("DESCRIBE session_products");
        $columns = $stmt->fetchAll();
        
        echo "<h3>بنية الجدول:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // فحص البيانات الموجودة
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM session_products WHERE session_id = ?");
        $stmt->execute([$test_session_id]);
        $count = $stmt->fetch()['count'];
        
        echo "<p>عدد المنتجات في الجلسة $test_session_id: $count</p>";
        
        if ($count == 0) {
            echo "<p style='color: orange;'>⚠️ لا توجد منتجات مضافة لهذه الجلسة</p>";
            
            // إضافة منتج تجريبي
            echo "<h3>إضافة منتج تجريبي:</h3>";
            
            // التحقق من وجود منتج في cafeteria_items
            $product_stmt = $pdo->query("SELECT id FROM cafeteria_items LIMIT 1");
            $product = $product_stmt->fetch();
            
            if (!$product) {
                $pdo->exec("INSERT INTO cafeteria_items (name, price, category, client_id) VALUES ('مشروب تجريبي', 5.00, 'مشروبات', 1)");
                $product_id = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي: $product_id</p>";
            } else {
                $product_id = $product['id'];
                echo "<p style='color: blue;'>ℹ️ استخدام المنتج الموجود: $product_id</p>";
            }
            
            // إضافة المنتج للجلسة
            $stmt = $pdo->prepare("INSERT INTO session_products (session_id, product_id, quantity, price, total_price) VALUES (?, ?, 2, 5.00, 10.00)");
            $stmt->execute([$test_session_id, $product_id]);
            echo "<p style='color: green;'>✅ تم إضافة منتج تجريبي للجلسة</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ جدول session_products غير موجود</p>";
        
        // إنشاء الجدول
        echo "<h3>إنشاء جدول session_products:</h3>";
        $create_table = "
            CREATE TABLE session_products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                unit_price DECIMAL(10,2) NULL,
                total_price DECIMAL(10,2) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_session_products_session_id (session_id),
                INDEX idx_session_products_product_id (product_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($create_table);
        echo "<p style='color: green;'>✅ تم إنشاء جدول session_products</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</p>";
}

echo "<h2>4. اختبار API مرة أخرى بعد التحضيرات</h2>";

// إعادة اختبار API
$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        'Cookie: ' . session_name() . '=' . session_id()
    ],
    CURLOPT_TIMEOUT => 10
]);

$response = curl_exec($curl);
$http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
curl_close($curl);

echo "<p style='color: blue;'>ℹ️ HTTP Code: $http_code</p>";

if ($http_code == 200) {
    echo "<p style='color: green;'>✅ API يعمل الآن بشكل صحيح!</p>";
    
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<p style='color: green;'>✅ تم جلب البيانات بنجاح</p>";
        echo "<p>عدد المنتجات: " . count($data['products']) . "</p>";
        echo "<p>التكلفة الإجمالية: " . $data['total_cost'] . " ج.م</p>";
    }
} else {
    echo "<p style='color: red;'>❌ لا يزال هناك خطأ في API</p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
}

echo "<h2>5. اختبار JavaScript</h2>";
?>

<div id="js-test-area" style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa;">
    <h3>اختبار تحميل المنتجات عبر JavaScript:</h3>
    <button onclick="testLoadProducts()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        اختبار تحميل المنتجات
    </button>
    <div id="js-results" style="margin-top: 15px;"></div>
    <div id="products-display" style="margin-top: 15px; border: 1px solid #ddd; border-radius: 5px; padding: 15px; background: white;"></div>
</div>

<script>
const testSessionId = <?php echo $test_session_id; ?>;

function testLoadProducts() {
    const resultsDiv = document.getElementById('js-results');
    const displayDiv = document.getElementById('products-display');

    resultsDiv.innerHTML = '<p style="color: blue;"><i class="fas fa-spinner fa-spin"></i> جاري تحميل المنتجات...</p>';
    displayDiv.innerHTML = '';

    console.log('Testing API call to:', `client/api/get_session_products.php?session_id=${testSessionId}`);

    fetch(`client/api/get_session_products.php?session_id=${testSessionId}`)
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response.text();
        })
        .then(responseText => {
            console.log('Raw response:', responseText);

            try {
                const data = JSON.parse(responseText);
                console.log('Parsed data:', data);

                if (data.success) {
                    resultsDiv.innerHTML = '<p style="color: green;"><strong>✅ نجح الاختبار!</strong></p>';

                    if (data.products && data.products.length > 0) {
                        let html = '<h4>المنتجات المضافة:</h4>';
                        let totalCost = 0;

                        data.products.forEach(product => {
                            totalCost += parseFloat(product.total);
                            html += `
                                <div style="border: 1px solid #eee; padding: 10px; margin: 5px 0; border-radius: 3px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <h6 style="margin: 0;">${product.product_name}</h6>
                                            <small style="color: #666;">${product.quantity} × ${product.price} ج.م</small>
                                        </div>
                                        <div>
                                            <span style="font-weight: bold; color: #007bff;">${product.total} ج.م</span>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });

                        html += `<div style="border-top: 2px solid #007bff; padding-top: 10px; margin-top: 10px; text-align: right;">
                            <strong>إجمالي تكلفة المنتجات: ${totalCost.toFixed(2)} ج.م</strong>
                        </div>`;

                        displayDiv.innerHTML = html;
                    } else {
                        displayDiv.innerHTML = '<p style="color: #666; text-align: center;">لا توجد منتجات مضافة</p>';
                    }
                } else {
                    resultsDiv.innerHTML = `
                        <p style="color: red;"><strong>❌ خطأ:</strong> ${data.error}</p>
                    `;
                }
            } catch (jsonError) {
                console.error('JSON parsing error:', jsonError);
                resultsDiv.innerHTML = `
                    <p style="color: red;"><strong>❌ خطأ في تحليل JSON:</strong> ${jsonError.message}</p>
                    <details>
                        <summary>الاستجابة الخام</summary>
                        <pre style="background: #ffe6e6; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;">${responseText}</pre>
                    </details>
                `;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            resultsDiv.innerHTML = `
                <p style="color: red;"><strong>❌ خطأ في الشبكة:</strong> ${error.message}</p>
            `;
        });
}

// تشغيل الاختبار تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(testLoadProducts, 1000);
});
</script>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th {
    background: #007bff !important;
    color: white !important;
}

pre {
    font-size: 12px;
}

#js-test-area {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
